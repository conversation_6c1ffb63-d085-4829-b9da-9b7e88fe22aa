// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef MODULE__VIDEO_ENGINE_HPP_
#define MODULE__VIDEO_ENGINE_HPP_

#include "common/base_module.hpp"
#include "common/topics/video_mode_control.hpp"
#include "utils/api_client.h"
#include <atomic>
#include <mutex>
#include <thread>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/avutil.h>
#include <libswresample/swresample.h>
#include <libswscale/swscale.h>
}

// 前向声明launch_main函数
extern int launch_main();

namespace aby_box {

// 摄像头错误类型枚举
enum class CameraErrorType {
  NONE = 0,
  INITIALIZATION_FAILED,
  SENSOR_DISCONNECTED,
  API_CALL_FAILED,
  MEMORY_ERROR,
  THREAD_CRASHED,
  UNKNOWN_ERROR
};

class VideoEngine : public BaseModule {
public:
  explicit VideoEngine(const std::string &module_name)
      : BaseModule(module_name), is_running_(false), 
        camera_failed_(false) {}
  ~VideoEngine() {}
  bool init() override;
  bool start() override;
  bool stop() override;
  void join() override;

  // 错误处理相关方法
  void reportCameraError(CameraErrorType error_type, const std::string& error_msg);
  bool isCameraHealthy() const;

  // 视频模式控制方法
  bool isGrayModeEnabled() const { return gray_mode_enabled_.load(); }

private:
  void recording_loop();
  void logCameraError(CameraErrorType error_type, const std::string& error_msg);
  void video_mode_subscriber();  // uORB监听线程

  std::atomic<bool> is_running_;
  std::thread recording_thread_;
  std::thread video_mode_thread_;  // 视频模式监听线程

  // 错误处理相关成员
  std::atomic<bool> camera_failed_;  // 标记摄像头是否已失效
  std::mutex error_mutex_;
  std::string last_error_message_;

  // 视频模式控制相关成员
  std::atomic<bool> gray_mode_enabled_{false};  // 是否启用灰度模式
};
} // namespace aby_box

#endif // VIDEO_ENGINE_HPP_